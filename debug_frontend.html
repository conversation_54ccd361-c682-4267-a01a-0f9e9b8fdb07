<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-unknown { background-color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端调试工具</h1>
        
        <div id="basicInfo" class="test-section info">
            <h3>基础信息</h3>
            <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>用户代理:</strong> <span id="userAgent"></span></p>
            <p><strong>网络状态:</strong> <span class="status-indicator" id="networkIndicator"></span><span id="networkStatus"></span></p>
            <p><strong>本地存储支持:</strong> <span id="localStorageSupport"></span></p>
        </div>

        <div id="scriptLoading" class="test-section warning">
            <h3>脚本加载检查</h3>
            <p>检查关键JavaScript文件是否正确加载...</p>
            <button onclick="checkScriptLoading()">检查脚本</button>
            <div id="scriptResults"></div>
        </div>

        <div id="apiClientTest" class="test-section warning">
            <h3>API客户端测试</h3>
            <p>检查apiClient对象是否可用...</p>
            <button onclick="testApiClient()">测试API客户端</button>
            <div id="apiClientResults"></div>
        </div>

        <div id="serverConnection" class="test-section warning">
            <h3>服务器连接测试</h3>
            <p>测试与后端服务器的连接...</p>
            <button onclick="testServerConnection()">测试连接</button>
            <div id="serverResults"></div>
        </div>

        <div id="loginSimulation" class="test-section warning">
            <h3>登录模拟测试</h3>
            <p>模拟登录过程...</p>
            <button onclick="simulateLogin()">模拟登录</button>
            <div id="loginResults"></div>
        </div>

        <div id="consoleLog" class="test-section">
            <h3>控制台日志</h3>
            <pre id="logOutput">等待日志输出...\n</pre>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        function clearLog() {
            document.getElementById('logOutput').textContent = '日志已清空...\n';
        }

        // 更新基础信息
        function updateBasicInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...';
            
            const isOnline = navigator.onLine;
            const indicator = document.getElementById('networkIndicator');
            const status = document.getElementById('networkStatus');
            
            if (isOnline) {
                indicator.className = 'status-indicator status-online';
                status.textContent = '在线';
            } else {
                indicator.className = 'status-indicator status-offline';
                status.textContent = '离线';
            }
            
            document.getElementById('localStorageSupport').textContent = 
                typeof(Storage) !== "undefined" ? '支持' : '不支持';
        }

        // 检查脚本加载
        function checkScriptLoading() {
            const resultsDiv = document.getElementById('scriptResults');
            const section = document.getElementById('scriptLoading');
            
            log('开始检查脚本加载状态...');
            
            let results = '<h4>脚本检查结果:</h4><ul>';
            
            // 检查apiClient是否存在
            if (typeof apiClient !== 'undefined') {
                results += '<li style="color: green;">✅ apiClient 对象已加载</li>';
                log('apiClient 对象检查通过', 'success');
            } else {
                results += '<li style="color: red;">❌ apiClient 对象未找到</li>';
                log('apiClient 对象未找到', 'error');
            }
            
            // 检查fetch API支持
            if (typeof fetch !== 'undefined') {
                results += '<li style="color: green;">✅ Fetch API 支持</li>';
                log('Fetch API 支持检查通过', 'success');
            } else {
                results += '<li style="color: red;">❌ Fetch API 不支持</li>';
                log('Fetch API 不支持', 'error');
            }
            
            // 检查Promise支持
            if (typeof Promise !== 'undefined') {
                results += '<li style="color: green;">✅ Promise 支持</li>';
                log('Promise 支持检查通过', 'success');
            } else {
                results += '<li style="color: red;">❌ Promise 不支持</li>';
                log('Promise 不支持', 'error');
            }
            
            results += '</ul>';
            resultsDiv.innerHTML = results;
            
            if (typeof apiClient !== 'undefined' && typeof fetch !== 'undefined') {
                section.className = 'test-section success';
            } else {
                section.className = 'test-section error';
            }
        }

        // 测试API客户端
        function testApiClient() {
            const resultsDiv = document.getElementById('apiClientResults');
            const section = document.getElementById('apiClientTest');
            
            log('开始测试API客户端...');
            
            if (typeof apiClient === 'undefined') {
                resultsDiv.innerHTML = '<p style="color: red;">❌ apiClient 未定义，请检查脚本加载</p>';
                section.className = 'test-section error';
                log('API客户端测试失败：未定义', 'error');
                return;
            }
            
            let results = '<h4>API客户端测试结果:</h4><ul>';
            
            // 检查baseURL
            if (apiClient.baseURL) {
                results += `<li style="color: green;">✅ Base URL: ${apiClient.baseURL}</li>`;
                log(`Base URL 配置正确: ${apiClient.baseURL}`, 'success');
            } else {
                results += '<li style="color: red;">❌ Base URL 未配置</li>';
                log('Base URL 未配置', 'error');
            }
            
            // 检查方法是否存在
            const methods = ['login', 'register', 'getInventory', 'request'];
            methods.forEach(method => {
                if (typeof apiClient[method] === 'function') {
                    results += `<li style="color: green;">✅ 方法 ${method} 存在</li>`;
                    log(`API方法 ${method} 检查通过`, 'success');
                } else {
                    results += `<li style="color: red;">❌ 方法 ${method} 不存在</li>`;
                    log(`API方法 ${method} 不存在`, 'error');
                }
            });
            
            results += '</ul>';
            resultsDiv.innerHTML = results;
            section.className = 'test-section success';
        }

        // 测试服务器连接
        async function testServerConnection() {
            const resultsDiv = document.getElementById('serverResults');
            const section = document.getElementById('serverConnection');
            
            log('开始测试服务器连接...');
            resultsDiv.innerHTML = '<p>正在测试连接...</p>';
            
            try {
                const response = await fetch('http://localhost:3000/api/health');
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <h4>服务器连接成功 ✅</h4>
                        <ul>
                            <li><strong>状态:</strong> ${data.message}</li>
                            <li><strong>版本:</strong> ${data.version}</li>
                            <li><strong>运行时间:</strong> ${Math.floor(data.uptime)}秒</li>
                            <li><strong>环境:</strong> ${data.environment}</li>
                        </ul>
                    `;
                    section.className = 'test-section success';
                    log('服务器连接测试成功', 'success');
                } else {
                    throw new Error('服务器返回错误状态');
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h4>服务器连接失败 ❌</h4>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p><strong>建议:</strong></p>
                    <ul>
                        <li>确保服务器正在运行: <code>node app.js</code></li>
                        <li>检查端口3000是否被占用</li>
                        <li>检查防火墙设置</li>
                        <li>确认服务器地址: http://localhost:3000</li>
                    </ul>
                `;
                section.className = 'test-section error';
                log(`服务器连接测试失败: ${error.message}`, 'error');
            }
        }

        // 模拟登录
        async function simulateLogin() {
            const resultsDiv = document.getElementById('loginResults');
            const section = document.getElementById('loginSimulation');
            
            log('开始模拟登录测试...');
            resultsDiv.innerHTML = '<p>正在模拟登录...</p>';
            
            if (typeof apiClient === 'undefined') {
                resultsDiv.innerHTML = '<p style="color: red;">❌ apiClient 未定义，无法进行登录测试</p>';
                section.className = 'test-section error';
                log('登录模拟失败：apiClient未定义', 'error');
                return;
            }
            
            try {
                const result = await apiClient.login('sysadmin', 'sys123', 'system_admin');
                
                if (result.success) {
                    resultsDiv.innerHTML = `
                        <h4>登录模拟成功 ✅</h4>
                        <ul>
                            <li><strong>用户:</strong> ${result.user.real_name}</li>
                            <li><strong>用户名:</strong> ${result.user.username}</li>
                            <li><strong>角色:</strong> ${result.user.role}</li>
                            <li><strong>ID:</strong> ${result.user.id}</li>
                        </ul>
                    `;
                    section.className = 'test-section success';
                    log('登录模拟测试成功', 'success');
                } else {
                    resultsDiv.innerHTML = `
                        <h4>登录模拟失败 ❌</h4>
                        <p><strong>错误:</strong> ${result.message}</p>
                    `;
                    section.className = 'test-section error';
                    log(`登录模拟失败: ${result.message}`, 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h4>登录模拟异常 ❌</h4>
                    <p><strong>错误:</strong> ${error.message}</p>
                `;
                section.className = 'test-section error';
                log(`登录模拟异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时执行
        window.onload = function() {
            updateBasicInfo();
            log('调试工具已加载');
            
            // 自动执行基础检查
            setTimeout(checkScriptLoading, 500);
            setTimeout(testApiClient, 1000);
        };

        // 监听网络状态变化
        window.addEventListener('online', () => {
            updateBasicInfo();
            log('网络连接已恢复');
        });

        window.addEventListener('offline', () => {
            updateBasicInfo();
            log('网络连接已断开');
        });

        // 捕获全局错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>

    <!-- 尝试加载API客户端 -->
    <script src="scripts/api-client.js"></script>
</body>
</html>
