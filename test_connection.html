<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 仓库管理系统 - 服务器连接测试</h1>
        
        <div id="healthCheck" class="test-section loading">
            <h3>1. 服务器健康检查</h3>
            <p>正在检查服务器状态...</p>
            <button onclick="testHealth()">重新测试</button>
        </div>

        <div id="loginTest" class="test-section loading">
            <h3>2. 登录功能测试</h3>
            <p>正在测试登录API...</p>
            <button onclick="testLogin()">测试登录</button>
        </div>

        <div id="inventoryTest" class="test-section loading">
            <h3>3. 库存数据测试</h3>
            <p>正在测试库存API...</p>
            <button onclick="testInventory()">测试库存</button>
        </div>

        <div id="networkInfo" class="test-section">
            <h3>4. 网络信息</h3>
            <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>服务器地址:</strong> http://localhost:3000</p>
            <p><strong>在线状态:</strong> <span id="onlineStatus"></span></p>
        </div>

        <div id="logs" class="test-section">
            <h3>5. 测试日志</h3>
            <pre id="logContent">开始测试...\n</pre>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 更新网络信息
        function updateNetworkInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('onlineStatus').textContent = navigator.onLine ? '在线' : '离线';
        }

        // 测试服务器健康状态
        async function testHealth() {
            const element = document.getElementById('healthCheck');
            element.className = 'test-section loading';
            element.innerHTML = '<h3>1. 服务器健康检查</h3><p>正在检查...</p><button onclick="testHealth()">重新测试</button>';
            
            log('开始健康检查...');
            
            try {
                const response = await fetch('http://localhost:3000/api/health');
                const data = await response.json();
                
                if (data.success) {
                    element.className = 'test-section success';
                    element.innerHTML = `
                        <h3>1. 服务器健康检查 ✅</h3>
                        <p><strong>状态:</strong> ${data.message}</p>
                        <p><strong>版本:</strong> ${data.version}</p>
                        <p><strong>运行时间:</strong> ${Math.floor(data.uptime)}秒</p>
                        <p><strong>环境:</strong> ${data.environment}</p>
                        <button onclick="testHealth()">重新测试</button>
                    `;
                    log('✅ 服务器健康检查通过');
                } else {
                    throw new Error('服务器返回错误状态');
                }
            } catch (error) {
                element.className = 'test-section error';
                element.innerHTML = `
                    <h3>1. 服务器健康检查 ❌</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p><strong>建议:</strong> 请确保服务器正在运行在 http://localhost:3000</p>
                    <button onclick="testHealth()">重新测试</button>
                `;
                log(`❌ 健康检查失败: ${error.message}`);
            }
        }

        // 测试登录功能
        async function testLogin() {
            const element = document.getElementById('loginTest');
            element.className = 'test-section loading';
            element.innerHTML = '<h3>2. 登录功能测试</h3><p>正在测试...</p><button onclick="testLogin()">测试登录</button>';
            
            log('开始登录功能测试...');
            
            try {
                const response = await fetch('http://localhost:3000/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'sysadmin',
                        password: 'sys123',
                        role: 'system_admin'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    element.className = 'test-section success';
                    element.innerHTML = `
                        <h3>2. 登录功能测试 ✅</h3>
                        <p><strong>登录成功:</strong> ${data.message}</p>
                        <p><strong>用户:</strong> ${data.user.real_name} (${data.user.username})</p>
                        <p><strong>角色:</strong> ${data.user.role}</p>
                        <button onclick="testLogin()">测试登录</button>
                    `;
                    log('✅ 登录功能测试通过');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                element.className = 'test-section error';
                element.innerHTML = `
                    <h3>2. 登录功能测试 ❌</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <button onclick="testLogin()">测试登录</button>
                `;
                log(`❌ 登录测试失败: ${error.message}`);
            }
        }

        // 测试库存功能
        async function testInventory() {
            const element = document.getElementById('inventoryTest');
            element.className = 'test-section loading';
            element.innerHTML = '<h3>3. 库存数据测试</h3><p>正在测试...</p><button onclick="testInventory()">测试库存</button>';
            
            log('开始库存数据测试...');
            
            try {
                const response = await fetch('http://localhost:3000/api/inventory');
                const data = await response.json();
                
                if (data.success) {
                    element.className = 'test-section success';
                    element.innerHTML = `
                        <h3>3. 库存数据测试 ✅</h3>
                        <p><strong>状态:</strong> 数据获取成功</p>
                        <p><strong>物品数量:</strong> ${data.items.length}个</p>
                        <p><strong>示例物品:</strong> ${data.items[0]?.name || '无'}</p>
                        <button onclick="testInventory()">测试库存</button>
                    `;
                    log('✅ 库存数据测试通过');
                } else {
                    throw new Error('库存数据获取失败');
                }
            } catch (error) {
                element.className = 'test-section error';
                element.innerHTML = `
                    <h3>3. 库存数据测试 ❌</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <button onclick="testInventory()">测试库存</button>
                `;
                log(`❌ 库存测试失败: ${error.message}`);
            }
        }

        // 页面加载时执行
        window.onload = function() {
            updateNetworkInfo();
            log('页面加载完成，开始自动测试...');
            
            // 自动执行所有测试
            setTimeout(testHealth, 500);
            setTimeout(testLogin, 1500);
            setTimeout(testInventory, 2500);
        };

        // 监听网络状态变化
        window.addEventListener('online', () => {
            updateNetworkInfo();
            log('网络连接已恢复');
        });

        window.addEventListener('offline', () => {
            updateNetworkInfo();
            log('网络连接已断开');
        });
    </script>
</body>
</html>
